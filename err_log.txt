app_1    | updateSquareProducts error:
app_1    | ApiError: Response status code was not ok: 400.
app_1    |     at /home/<USER>/server/node_modules/@apimatic/core/lib/http/requestBuilder.js:367:23
app_1    |     at DefaultRequestBuilder.<anonymous> (/home/<USER>/server/node_modules/@apimatic/core/lib/http/requestBuilder.js:179:50)
app_1    |     at step (/home/<USER>/server/node_modules/tslib/tslib.js:144:27)
app_1    |     at Object.next (/home/<USER>/server/node_modules/tslib/tslib.js:125:57)
app_1    |     at fulfilled (/home/<USER>/server/node_modules/tslib/tslib.js:115:62)
app_1    |     at process.processTicksAndRejections (node:internal/process/task_queues:95:5) {
app_1    |   request: {
app_1    |     method: 'POST',
app_1    |     url: 'https://connect.squareup.com/v2/catalog/batch-upsert',
app_1    |     headers: {
app_1    |       'Content-Type': 'application/json',
app_1    |       authorization: 'Bearer EAAAESbBMEWSE0nV1dowhrd24RujQ0OGUVfZxKq75yaiOJVY58Hz1a4dQ06RvRLv',
app_1    |       'user-agent': 'Square-TypeScript-SDK/23.0.0 (2022-10-19) node/18.20.4 (linux) ',
app_1    |       'Square-Version': '2022-10-19'
app_1    |     },
app_1    |     body: {
app_1    |       type: 'text',
app_1    |       content: '{"idempotency_key":"e4a20003-1b2b-4225-b486-32af50216ee7","batches":[{"objects":[{"type":"ITEM_VARIATION","id":"BEJB7QD3TRHABUDJPZT23W3T","version":1754548986771,"item_variation_data":{"item_id":"QJ3XCFU5YDPL2PR7ZJ2C3VJY","sku":"SADBZT1721","pricing_type":"FIXED_PRICING","price_money":{"amount":775,"currency":"USD"},"track_inventory":true,"sellable":true,"stockable":true}},{"type":"ITEM","id":"QJ3XCFU5YDPL2PR7ZJ2C3VJY","version":1754548986771,"item_data":{"name":"Exterior Screws 2\\"","category_id":"W7EJKXBDETAKH5Z5BOMIGXPT","tax_ids":["KYWG2XEOZIYOIS7HHKIQUBSH"],"variations":[{"type":"ITEM_VARIATION","id":"BEJB7QD3TRHABUDJPZT23W3T","updated_at":"2025-08-07T06:43:06.771Z","version":1754548986771,"is_deleted":false,"present_at_all_locations":true,"item_variation_data":{"item_id":"QJ3XCFU5YDPL2PR7ZJ2C3VJY","name":"","sku":"SADBZT1721","ordinal":1,"pricing_type":"FIXED_PRICING","price_money":{"amount":675,"currency":"USD"},"track_inventory":true,"sellable":true,"stockable":true}}],"product_type":"REGULAR","skip_modifier_screen":false}},{"type":"ITEM_VARIATION","id":"TU6RFKOHDSLKOFBE7E4BSMVC","version":1754365288222,"item_variation_data":{"item_id":"QUIHGZTUAUFNKJF6RZWOJ7XX","sku":"SADBZT0547","pricing_type":"FIXED_PRICING","price_money":{"amount":125,"currency":"USD"},"track_inventory":true,"sellable":true,"stockable":true}},{"type":"ITEM","id":"DAS3WGW2WG3UHRLOVWRRJGY2","version":1754365288222,"item_data":{"name":"BWF - Double Gang NM Outlet Box - Gray","category_id":"UJCLZL4PHYG5FHK7TGPDQU23","tax_ids":["KYWG2XEOZIYOIS7HHKIQUBSH"],"variations":[{"type":"ITEM_VARIATION","id":"AL5ZTTMTC3KOSFHYT6K6YY55","updated_at":"2024-03-06T16:54:31.911Z","version":1709744071911,"is_deleted":false,"present_at_all_locations":true,"item_variation_data":{"item_id":"DAS3WGW2WG3UHRLOVWRRJGY2","name":"","sku":"087115074102","ordinal":1,"pricing_type":"FIXED_PRICING","price_money":{"amount":950,"currency":"USD"},"location_overrides":[{"location_id":"LDZMD0H4RJ765","track_inventory":true}],"track_inventory":true,"sellable":true,"stockable":true}}],"product_type":"REGULAR","skip_modifier_screen":false}}]}]}'
app_1    |     }
app_1    |   },
app_1    |   statusCode: 400,
app_1    |   headers: {
app_1    |     date: 'Thu, 07 Aug 2025 06:51:50 GMT',
app_1    |     'content-type': 'application/json',
app_1    |     'transfer-encoding': 'chunked',
app_1    |     connection: 'close',
app_1    |     'cf-ray': '96b4d909388fef61-IAD',
app_1    |     'cf-cache-status': 'DYNAMIC',
app_1    |     'strict-transport-security': 'max-age=631152000; includeSubDomains; preload',
app_1    |     vary: 'Origin, Accept-Encoding',
app_1    |     'square-version': '2022-10-19',
app_1    |     'x-content-type-options': 'nosniff',
app_1    |     'x-download-options': 'noopen',
app_1    |     'x-envoy-decorator-operation': '/v2/catalog/**',
app_1    |     'x-frame-options': 'SAMEORIGIN',
app_1    |     'x-permitted-cross-domain-policies': 'none',
app_1    |     'x-speleo-traceid': 'CDN-51db8f01-cccf-48e3-8e88-7ba301df40d6',
app_1    |     'x-sq-dc': 'aws',
app_1    |     'x-sq-istio-migration-ingress-proxy': 'sq-envoy',
app_1    |     'x-sq-istio-migration-ingress-region': 'us-west-2',
app_1    |     'x-sq-region': 'us-west-2',
app_1    |     'x-xss-protection': '1; mode=block',
app_1    |     'set-cookie': [
app_1    |       '__cf_bm=4CEKiFnd1Tk1x8zfOUq51Bk.kF8snkoh_gw3ysGVIzc-1754549510-1.0.1.1-UYMqVD2N6AHgORftWosWI_TNDWcDUCt8N_g7RIwquVKO2z37_KRoniSLt8ZxynhYbljv9snT7fXpS3pbiBCbfZy7Komxp9ML7KQ_eK1GD4I; path=/; expires=Thu, 07-Aug-25 07:21:50 GMT; domain=.connect.squareup.com; HttpOnly; Secure; SameSite=None'
app_1    |     ],
app_1    |     server: 'cloudflare'
app_1    |   },
app_1    |   body: '{"errors":[{"category":"INVALID_REQUEST_ERROR","code":"INVALID_VALUE","detail":"Invalid object: Invalid Object with Id: BEJB7QD3TRHABUDJPZT23W3T\\n[merchant_token=MLH03SANETDYH] Duplicate object BEJB7QD3TRHABUDJPZT23W3T."}]}',
app_1    |   result: [Object: null prototype] { errors: [ [Object: null prototype] ] },
app_1    |   errors: [
app_1    |     [Object: null prototype] {
app_1    |       category: 'INVALID_REQUEST_ERROR',
app_1    |       code: 'INVALID_VALUE',
app_1    |       detail: 'Invalid object: Invalid Object with Id: BEJB7QD3TRHABUDJPZT23W3T\n' +
app_1    |         '[merchant_token=MLH03SANETDYH] Duplicate object BEJB7QD3TRHABUDJPZT23W3T.'
app_1    |     }
app_1    |   ]
app_1    | }