import { Client, Environment, ApiError, Employees<PERSON><PERSON> } from "square";
import axios from 'axios';
import crypto from 'crypto';
import Fastify from 'fastify';
import { createClient } from 'redis';
import * as cron from 'node-cron'

import fs from 'fs'


const fastify = Fastify({ logger: false })

const client = new Client({
  accessToken: process.env.SQUARE_ACCESS_TOKEN,
  environment: Environment.Production,
  httpClientOptions: {
    retryConfig: {
      maxNumberOfRetries: 100,
      maximumRetryWaitTime: 10000,
    }
  }
});

const redisClient = createClient({
  url: 'redis://redis:6379'
})

const { locationsApi } = client;
const squareIDs = []

const sortlyURL = 'https://api.sortly.co/api/v1'
//Make this more secure eventually
const sortlyOpts = {
  headers: {
    Accept: 'application/json',
    Authorization: `Bearer ${process.env.SORTLY_TOKEN}`
  }
}
//Sortly Folder IDs
const warehouseID = "45029686"
const testID = "45452042"

let combinedData
let locationId
let sortly_catalog
let sortly_redis
let square_catalog
let sortly_status
// let square_redis
let measurement_list = []

async function app() {
  redisClient.on('error', (err) => console.log('redis client error', err))
  await redisClient.connect();
  try {
    await fastify.listen({ host: '0.0.0.0', port: 3000 })
  } catch (err) {
    fastify.log.error(err)
    process.exit(1)
  }

  locationId = await getLocationId();

  sortly_redis = await redisClient.get('sortly')
  if (!sortly_redis) {
    sortly_catalog = await getCatalog('sortly')
  } else {
    console.log('using redis for sortly')
    sortly_catalog = JSON.parse(sortly_redis)
  }
  /**
   * they dont have variants
   *
    const sortly_variants = await getVariants()
    console.log(sortly_catalog)
    sortly_catalog.forEach(product => {
      sortly_variants.forEach(variant => {
        if (product.id === variant.id) {
          product.variations = variant.option_values
        }
      })
    })
  */


  // square_redis = await redisClient.get('square')
  // if (!square_redis) {
  square_catalog = await getCatalog('square')
  // } else {
  //   console.log('using redis for square')
  //   square_catalog = JSON.parse(square_redis, (key,value)=>{
  //     if (typeof value === "string" && /^\d+n$/.test(value)) {
  //       return BigInt(value.substr(0, value.length - 1));
  //     }
  //     return value
  //   })
  // }
  combinedData = await matchData(square_catalog, sortly_catalog)
  updateSquareInventory(combinedData)
  console.log("Inventory update completed. Awaiting further updates...");

  // cron.schedule('0 0 * * *', async () => {
  //   await syncInventories()
  //   console.log('syncInventories job completed?')
  // })

  //Temp function to add skus to uploaded products.
  //addSkus()

}

app()

async function syncInventories() {
  //call fetch sortly catalog to get new list
  sortly_catalog = await getCatalog('sortly')
  let date = new Date()
  console.log(date.toLocaleString())
  console.log('Fetching new Sortly data: ', sortly_catalog.length)
  combinedData = await matchData(square_catalog, sortly_catalog)
  console.log('Updating inventory')
  let jobDone = await updateSquareInventory(combinedData)
  console.log('syncInventories -- updateSquareInventory succeeded? ', jobDone)
  return true
}
async function addSkus() {
  // console.log('sluuu')
  let batches = []
  let upsertBody
  // console.log(combinedData.length)
  console.dir(combinedData[0].square_data.variations, { depth: null })
  combinedData.forEach((product, index) => {

    let toPush = { ...product.square_data.variations[0] }
    let arr_key = Math.floor(index / 1000);
    let working_arr = batches[arr_key] || (batches[arr_key] = { objects: [] });

    toPush.itemVariationData.sku = product.sku
    toPush.version = Number(toPush.version)
    toPush.itemVariationData.priceMoney.amount = Number(toPush.itemVariationData.priceMoney.amount)

    working_arr.objects.push(toPush);
    // if (batches.length === 0 || batches[batches.length - 1].objects.length === 1000) {
    //   console.log('hmm', batches.length)
    //   console.log('i see', batches[batches.length - 1])
    //   batches.push({
    //     objects: [toPush]
    //   })
    // }
    //if (batches[batches.length - 1].objects.length < 1000) {
    //batches[batches.length - 1].objects.push(toPush)

    //}
  })
  if (batches.length < 10) {
    upsertBody = {
      idempotencyKey: crypto.randomUUID(),
      batches: batches
    }
  } else {
    // create addition upsertBodys if needed
  }
  try {
    const response = await client.catalogApi.batchUpsertCatalogObjects(upsertBody)
    console.log('skus added')
    return

  } catch (err) {
    console.log(err)
  }
}
//Square Webhooks

//Inventory Updated Hook

fastify.get('/', async (request, reply) => {
  reply.code(200)
  reply.send('hello')
})

fastify.post('/webhooks/updateInventory', async (request, reply) => {
  reply.code(200)
  console.log('incoming square webhook')
  let data = request.body.data
  console.dir(data, { depth: null })
  updateSortlyInventory(data, combinedData)
})
//Invoice Created
fastify.post('/webhooks/invoiceCreated', async (request, reply) => {
  reply.code(200)
  console.log('incoming invoice created webhook')
  console.dir(request.body.data, { depth: null })
  let invoiceId = request.body.data.id
  let invoiceNumber = request.body.data.object.invoice.invoice_number
  if (invoiceNumber.includes('NC')) {
    console.log('Do not need to update invoice user inputed NC by hand')
    reply.send('NC already existed')
    return
  }
  let updated_number = `NC${request.body.data.object.invoice.invoice_number}`

  updateInvoiceNumber(invoiceId, updated_number)
})

fastify.get('/api/updateSkus', async (request, reply) => {
  let toFix = combinedData.filter(product => product.sku.charAt(0) === '0')
  adjustSkus(toFix)
  reply.code(200)
})

fastify.get('/api/fixItems', async (request, reply) => {
  console.log('fixing')
  let toUpdate = []
  let productsToCheck = []
  for (const product of square_catalog) {
    let curProduct = { ...product.itemData }
    if (curProduct.variations.length === 1 && !curProduct.variations[0].itemVariationData.hasOwnProperty('trackInventory')) {
      let updatedProduct = {...curProduct.variations[0]}
      let matched = combinedData.find(obj => obj.square_id === curProduct.variations[0].id)
      if (!matched) {
        productsToCheck.push(curProduct)
      } else {
        let sku = matched.sku
       updatedProduct.itemVariationData.trackInventory = true
       updatedProduct.itemVariationData.sku = sku
       updatedProduct.itemVariationData.sellable = true
       updatedProduct.itemVariationData.stockable = true
       toUpdate.push(updatedProduct)
      }
    }
  }
  console.log('# of products to fix: ', toUpdate.length)
  console.log('# of products to check: ', productsToCheck.length )
  console.dir(toUpdate,{depth:null})
  //console.log(productsToCheck)
  await updateSquareProducts(toUpdate)
 //console.log(productsUpdated)
 reply.code(200)
  //console.dir(toUpdate,{depth:null})
})

fastify.post('/api/cleanDuplicates', async (request, reply) => {
  let skus = request.body.skus
})

//Sortly Webhook (can only have 1 sad face)
fastify.post('/sortly/webhook',async (request,reply)=>{
  console.log('webhook from sortly')
  console.dir(request.body)
  reply.send(200);
})
/**
 *
 * @param {array} products - list of products to fix skus
 * This function was added to remove leading 0 from skus
 * Temp function that can be used by calling els.yobo.dev/api/updateSkus
 * Shouldn't need to be used in the future since sku storing will be updated as well
 */
async function adjustSkus(products) {
  let batches = []
  let upsertBody
  products.forEach((product, index) => {
    let newSku = product.square_data.variations[0].itemVariationData.sku.substring(1)
    let toPush = { ...product.square_data.variations[0] }
    let arr_key = Math.floor(index / 1000)
    let working_arr = batches[arr_key] || (batches[arr_key] = { objects: [] })
    toPush.itemVariationData.sku = newSku
    toPush.version = Number(toPush.version)
    toPush.itemVariationData.priceMoney.amount = Number(toPush.itemVariationData.priceMoney.amount)
    working_arr.objects.push(toPush)
  })
  if (batches.length < 10) {
    upsertBody = {
      idempotencyKey: crypto.randomUUID(),
      batches: batches
    }
  }
  try {
    const response = await client.catalogApi.batchUpsertCatalogObjects(upsertBody)
    console.log('skus updated')
    return response
  } catch (err) {
    console.log(err)
  }
}
async function removeDuplicates(duplicates) {
  try {
    const response = await client.catalogApi.batchDeleteCatalogObjects({
      objectIds: duplicates
    })
    return response
  } catch (error) {
    console.log(error)
  }
}
async function updateInvoiceNumber(id, number) {
  console.log('invoice Id: ', id)
  try {
    const response = await client.invoicesApi.updateInvoice(id, {
      invoice: {
        version: 0,
        invoiceNumber: number
      },
      idempotencyKey: crypto.randomUUID()
    })
    return true
  } catch (error) {
    console.log(error)
    return false
  }
}
//Set Catalog for both Square and Sortly
async function getCatalog(location) {
  if (location === 'square') {
    try {
      console.log('fetching square catalog')
      let catalog = []
      // console.log('current measurements', measurement_list)
      let categoryResponse = await client.catalogApi.listCatalog(undefined, 'ITEM');
      let measurementResponse = await client.catalogApi.listCatalog(undefined, 'MEASUREMENT_UNIT')
      //should look into seeing about adding new units if they come up
      if (Object.keys(measurementResponse.result).length === 0) {
        // console.log('no measurements lets make some')
        measurement_list = await createMeasurementList(sortly_catalog)
        // console.log(measurement_list)
        // console.log('I finished making list of measurements')
        getCatalog("square")
      } else {
        measurementResponse.result.objects.forEach(measurement => {
          console.log(measurement)
          let measurementData = measurement.measurementUnitData.measurementUnit
          measurement_list.push({
            squareId: measurement.id,
            unit: setMeasurementUnit(measurementData[Object.keys(measurementData)[0]])
          })
        })
      }
      if (Object.keys(categoryResponse.result).length === 0) {
        // console.log('in here?')
        square_catalog = await createSquareCatalog(sortly_catalog)
      } else if (categoryResponse.result?.cursor) {
        let pageOne = categoryResponse.result.objects
        let cursor = categoryResponse.result.cursor
        catalog = await getMoreItems(cursor, pageOne)
      }
      //need to do this due to issue with BigInt and JSON
      let json = JSON.stringify(catalog, (key, value) =>
        typeof value === "bigint" ? value.toString() + "n" : value
      )
      await redisClient.set('square', json)
      return catalog
    } catch (error) {
      console.log(error);
    }
  } else {
    console.log('fetching sortly')
    let catalog = await fetchSortly()
    console.log('Sortly Length', catalog.length)
    await redisClient.set('sortly', JSON.stringify(catalog))
    return catalog
  }

}

//Fetching Sortly Main Folder
async function fetchSortly() {
  try {
    // First call going through parent location
    console.log(sortlyOpts);
    console.log(`${sortlyURL}/items?per_page=50&page=1&folder_id=${warehouseID}`);

    // Fetch the initial data from Sortly API
    const categoryResponse = await axios.get(`${sortlyURL}/items?per_page=50&page=1&folder_id=${warehouseID}`, sortlyOpts);
    let data = categoryResponse.data;
    let remainingLimit = +categoryResponse.headers["sortly-rate-limit-remaining"];
    let resetTimer = +categoryResponse.headers["sortly-rate-limit-reset"];
    let folders = [];
    let itemList = [];

    // Process the fetched data
    data.data.forEach((object) => {
      if (object.type === "folder") {
        folders.push({
          id: object.id,
          name: object.name
        });
      } else {
        object.category = "Warehouse Inventory";
        itemList.push(object);
      }
    });

    // If there are folders, traverse them; otherwise, return the item list
    if (folders.length > 0) {
      return await traverseFolders(remainingLimit, resetTimer, folders, itemList);
    } else {
      return itemList;
    }
  } catch (error) {
    console.error('Error fetching data from Sortly:', error);
    throw error;
  }
}


//Recursive function to go through all nested folders in Sortly to create one master list
async function traverseFolders(currentLimit, currentTimer, folders, currentItems) {

  let remainingLimit = currentLimit ? currentLimit : 1000
  let resetTimer = currentTimer ? currentTimer : 900
  let itemList = currentItems ? [...currentItems] : []
  let headers = {
    limit: [],
    time: []
  }
  let new_folders = []

  //Reset timer back to 15 minutes
  if (resetTimer <= 0) {
    resetTimer = 900
  }
  //If remainingLimit is 0 and time > 0 wait for reset
  if (remainingLimit === 0 && resetTimer > 0) {
    await sleep(resetTimer * 1000)
    resetTimer = 900
    remainingLimit = 1000
  }



  if (remainingLimit != 0) {
    //using axios.all is probably not a great ida as we cant check headers as far as I know before doing next request
    let categoryResponse = await axios.all(folders.map((folder) => axios(`${sortlyURL}/items?per_page=200&page=1&folder_id=${folder.id}&`, sortlyOpts)))
    categoryResponse.forEach((res) => {
      headers.limit.push(+res.headers['sortly-rate-limit-remaining'])
      headers.time.push(+res.headers['sortly-rate-limit-reset'])
      res.data.data.forEach((object) => {
        if (object.type === "folder") {
          let parentFolder = folders.find(folder => folder.id === object.parent_id) ? folders.find(folder => folder.id === object.parent_id).name : null
          let name = parentFolder ? `${parentFolder} - ${object.name}` : object.name
          new_folders.push({
            id: object.id,
            name: name
          })
        }
        if (object.type === "item") {
          object.category = folders.find(folder => folder.id === object.parent_id).name
          object.sku = object.sid
          if (object.label_url === null && object.label_url_extra != null) {
            object.sku = object.label_url_extra
          }
          itemList.push(object)
        }
      })
    })
    if (headers.limit.length && headers.time.length) {
      remainingLimit = getSmallest(headers.limit)
      resetTimer = getSmallest(headers.time)
    }
    if (folders.length > 0) {
      // console.log('more folders')
      return traverseFolders(remainingLimit, resetTimer, new_folders, itemList)
    } else {
      return itemList
    }
  }
}

//Check for Variant Groups and get values
async function getVariants() {
  const categoryResponse = await axios(`${sortlyURL}/item_groups?per_page=50&include=variants`, sortlyOpts)
  let item_groups = categoryResponse.data.item_groups
  let attributes = []
  let variants = item_groups
    .filter(group => group.variants.length > 0)
    .reduce((res, group) => {
      group.variants.forEach(variant => {
        res.push({
          id: variant.item_attributes.id,
          option_values: variant.option_value_ids.map(id => {
            let option_name = "Not found";
            group.attributes.forEach(attribute => {
              attribute.options.forEach(option => {
                if (option.id === id) {
                  option_name = option.name;
                }
              })
            })

            return option_name;
          })
        })
      })
      return res;
    }, [])

  return variants
}

//Match Square data with Sortly
async function matchData(square, sortly) {
  console.log('trying to match data')
  let matched = []
  let toUpdate = []
  let unmatched = []
  console.log('square count: ', square.length)
  console.log('sortly count: ', sortly.length)
  let allSquareIds = new Set(square.map(item => item.itemData.variations[0]?.itemVariationData?.sku));
  let unmatchedSquare = []
  let itemCount = 0
  let idList = []
  //Matching Sortly catalog to Square Catalog.
  //Not sure if there is a way to do both at the same time
  for (const product of sortly) {
    let sku
    if (!product.sku) {
      console.log('no sku set')
      sku = product.sid
      if (product.label_url === null && product.label_url_extra != null) {
        sku = product.label_url_extra
      }
    } else {
      sku = product.sku
    }

    // First try to match by name (existing behavior)
    let productsMatched = square.filter(item => item.itemData.name === product.name)
    //console.log('productsmatched', productsMatched)
    let productMatched

    if (productsMatched.length === 0) {
      // If no match by name, try to match by SKU
      console.log(`No match by name for ${product.name}, trying to match by SKU: ${sku}`)

      // Handle case where SKU might have a leading zero
      let skuToMatch = sku
      let skuWithoutLeadingZero = sku.charAt(0) === '0' ? sku.substring(1) : sku

      // Try to find a match by SKU
      let skuMatches = square.filter(item => {
        const itemSku = item.itemData.variations[0]?.itemVariationData?.sku
        return itemSku === skuToMatch || itemSku === skuWithoutLeadingZero
      })

      if (skuMatches.length > 0) {
        console.log(`Found match by SKU for ${product.name}. Square item name: ${skuMatches[0].itemData.name}`)
        productMatched = skuMatches[0]

        // Update the name in Square to match the new name in Sortly
        if (productMatched.itemData.name !== product.name) {
          console.log(`Updating name in Square from "${productMatched.itemData.name}" to "${product.name}"`)

          // Create an update object for the item
          let nameUpdate = {
            type: 'ITEM',
            id: productMatched.id,
            version: productMatched.version,
            itemData: {
              ...productMatched.itemData,
              name: product.name
            }
          }

          toUpdate.push(nameUpdate)
        }
      } else {
        productMatched = false
      }
    } else if (productsMatched.length === 1) {
      productMatched = productsMatched[0]
    } else {
      console.log('multiple matched items for ', product.sku)
      const referenceSku = productsMatched[0]?.itemData?.variations[0]?.itemVariationData?.sku;

      // Iterate over the array and check if all SKUs match the reference SKU
      const allMatch = productsMatched.every((item) => {
        const sku = item?.itemData?.variations[0]?.itemVariationData?.sku;
        return sku === referenceSku;
      });

      if (allMatch) {
        productMatched = productsMatched[1]
        let duplicateProducts = [...productsMatched]
        duplicateProducts.splice(1, 1)
        let duplicateIds = []
        for (const item of duplicateProducts) {
          duplicateIds.push(item.itemData.variations[0].itemVariationData.itemId)
        }
        await removeDuplicates(duplicateIds)
      } else {
        for (const matchedProduct of productsMatched) {
          console.log('multiple heres')
          console.log(matchedProduct.itemData.name)
          console.log(product.sku)
          console.dir(matchedProduct, { depth: null })
          let skuToMatch = sku
          if (sku.charAt(0) === '0') {
            skuToMatch = sku.substring(1)
          }
          if (skuToMatch === matchedProduct.itemData.variations[0].itemVariationData.sku) {
            console.log(matchedProduct.itemData.name, 'matched')
            productMatched = matchedProduct
          }
          // let category = await getCategory(matchedProduct.itemData.categoryId)
          // console.log(matchedProduct.itemData.variations[0].itemVariationData)
          // console.log(product.category)
          // if (category === product.category) {
          //   productMatched = matchedProduct
          //   // console.log(productMatched)
          // }
        }
      }
    }
    if (productMatched !== false) {
      //console.log('productMatched', productMatched)
      allSquareIds.delete(productMatched.itemData.variations[0].itemVariationData.sku);
      //console.log('productmatched !== false', productMatched)
      let sortlyPrice = Math.round((Math.abs(+product.price / 100) * 10000))
      let squarePrice = productMatched.itemData.variations[0].itemVariationData.priceMoney.amount
      if (BigInt(sortlyPrice) != squarePrice) {
        console.log('product to update (price): ')
        console.dir(productMatched, { depth: null })
        let productToUpdate = { ...productMatched }
        let priceUpdate = {
          type: 'ITEM_VARIATION',
          id: productMatched.itemData.variations[0].id,
          version: productMatched.itemData.variations[0].version,
          itemVariationData: {
            itemId: productMatched.itemData.variations[0].itemVariationData.itemId,
            pricingType: 'FIXED_PRICING',
            priceMoney: {
              amount: sortlyPrice,
              currency: 'USD'
            },
            trackInventory: true,
            sellable: true,
            sku: productMatched.itemData.variations[0].itemVariationData.sku,
            stockable: true
          }
        }
        console.log('method 1:')
        console.dir(priceUpdate, { depth: null })
        console.log('method 2 maybe:')
        console.dir(productToUpdate, { depth: null })
        toUpdate.push(priceUpdate)
      }
      let toPush = {
        product_name: product.name,
        square_id: productMatched.itemData.variations[0].id,
        square_data: productMatched.itemData,
        square_location_id: locationId,
        square_type: productMatched.type,
        sku: product.sku ? product.sku : sku,
        sortly_id: product.id,
        sortly_quantity: product.quantity.toString()
      }
      // if (productMatched.itemData.variations.length > 1) {
      //   productMatched.itemData.variations.forEach(variation => {
      //     if (product.variations.incudes(variation.itemVariationData.name)) {
      //       toPush.square_id = variation.id
      //       toPush.square_type = variation.type
      //     }
      //   })
      // }

      matched.push(toPush)
    } else {
      unmatched.push(product)
    }
  }
  /*   console.log('matched data count: ', matched.length)
    console.log('unmatched data count: ', unmatched.length)
    console.log('products to be added: ')
    console.dir(unmatched, { depth: null })
    console.log('number of products that dont use Sortly ID as a barcode:', itemCount)
    console.log(idList)
    console.log('Unmatched data?: ')
    console.dir(unmatched, { depth: null })
  */
  if (unmatched.length >= 1) {
    console.log('trying to create new item');
    let newItems = await createNewItem(unmatched)
    matched = [...matched, ...newItems]
  }
  //
  console.log("Square products that didn't get matched: ");
  for (const unmatchedId of allSquareIds) {
    const unmatchedProduct = square.find(item => item.itemData.variations[0].itemVariationData.sku === unmatchedId);
    let toPush = {}
    if (unmatchedProduct.itemData.variations.length > 1) {
      console.dir(unmatchedProduct, { depth: null })
    } else {
      toPush = {
        product_name: unmatchedProduct.itemData.name,
        square_id: unmatchedProduct.itemData.variations[0].id,
        square_data: unmatchedProduct.itemData,
        square_location_id: locationId,
        square_type: unmatchedProduct.type,
        sku: unmatchedProduct.itemData.variations[0].itemVariationData.sku,
        sortly_quantity: "0"
      }
      //console.log(toPush)
      matched.push(toPush)
    }
  }

  if (toUpdate.length) {
    await updateSquareProducts(toUpdate)
  }
  return matched
}
async function updateSquareProducts(data) {
  console.log('update square products', data.length);

  // Deduplicate helper using just native JS
  function deduplicateObjectsById(objects) {
    const seen = new Set();
    const deduped = [];
    for (const obj of objects) {
      if (!seen.has(obj.id)) {
        seen.add(obj.id);
        deduped.push(obj);
      }
    }
    return deduped;
  }

  // Chunk helper using native JS (no lodash)
  function chunkArray(array, size) {
    const result = [];
    for (let i = 0; i < array.length; i += size) {
      result.push(array.slice(i, i + size));
    }
    return result;
  }

  if (data.length > 100) {
    const batches = chunkArray(data, 100);
    for (const group of batches) {
      const batch = {
        idempotencyKey: crypto.randomUUID(),
        batches: [
          {
            objects: deduplicateObjectsById(group),
          },
        ],
      };

      try {
        const upsertResponse = await client.catalogApi.batchUpsertCatalogObjects(batch);
        console.log('Batch upsert successful');
      } catch (error) {
        console.log('updateSquareProducts error:');
        console.error(error);
      }
    }
  } else {
    const batch = {
      idempotencyKey: crypto.randomUUID(),
      batches: [
        {
          objects: deduplicateObjectsById(data),
        },
      ],
    };

    console.dir(batch, { depth: null });

    try {
      const upsertResponse = await client.catalogApi.batchUpsertCatalogObjects(batch);
      console.log('got upsertResponse');
      return true;
    } catch (error) {
      console.log('updateSquareProducts error:');
      console.error(error);
    }
  }

  return;
}

//Update Square Inventory from Sortly
async function updateSquareInventory(data) {
  console.log("updating inventory")

  let batches = [];
  // Square only allows for 100 inventory updates per request
  if (data.length > 100) {
    console.log("100+");
    let toBatch = chunk(data, 100);
    batches = createBatch(toBatch, "batchInventory");
    try {
      // Process all batches concurrently
      const responses = await Promise.all(
        batches.map(async (batch, index) => {
          console.log(`Processing batch ${index}`);
          return client.inventoryApi.batchChangeInventory(batch);
        })
      );
      console.log("All batch requests completed");
    } catch (error) {
      console.error("Error processing batches:", error);
    }
  }
   else {
    let batch = {
      idempotencyKey: crypto.randomUUID(),
      changes: []
    }
    data.map(item => {
      batch.changes.push({
        type: "PHYSICAL_COUNT",
        physicalCount: {
          referenceId: "sortly_connector",
          occurredAt: new Date().toISOString(),
          quantity: item.sortly_quantity,
          locationId: item.square_location_id,
          state: "IN_STOCK",
          catalogObjectId: item.square_id,
        }
      })
    })
    try {
      console.log(batch)
      const categoryResponse = await client.inventoryApi.batchChangeInventory(batch)
      console.log('got a categoryResponse')
      return true
    } catch (error) {
      console.log(error)
      return false
    }
  }

}

//Function to create seperate batches for given api request
function createBatch(data, type) {
  let batches = []
  if (type === 'batchInventory') {
    data.forEach(group => {
      let batch = {
        idempotencyKey: crypto.randomUUID(),
        changes: []
      }
      group.forEach(item => {
        batch.changes.push({
          type: "PHYSICAL_COUNT",
          physicalCount: {
            referenceId: "sortly_connector",
            occurredAt: new Date().toISOString(),
            quantity: item.sortly_quantity,
            locationId: item.square_location_id,
            state: "IN_STOCK",
            catalogObjectId: item.square_id,
          }
        })
      })
      batches.push(batch)
    })
  } else if (type === "batchUpsert") {
    data.forEach(group => {
      let batch = {
        idempotencyKey: crypto.randomUUID(),
        objects: []
      }
      group.forEach(item => {
        batch.objects.push(item)
      })
      batches.push(batch)
    })
  }
  return batches
}
//Function to get list of Locations associated with a Square account
async function getLocationId() {
  try {
    const categoryResponse = await client.locationsApi.listLocations()
    if (categoryResponse.result.locations.length > 1) {
      let locationIds = []
      categoryResponse.result.locations.forEach(location => {
        locationIds.push(location.id)
      })
      return locationIds
    } else {
      return categoryResponse.result.locations[0].id
    }

  } catch (error) {
    console.log(error)
  }
}
//Function to create square measurement unit catalog
async function createMeasurementList(sortly) {
  console.log('creating measurementlist');
  let measurements = []
  let upsertBody = {
    idempotencyKey: crypto.randomUUID(),
    batches: [{
      objects: []
    }]
  }
  console.log('sortly measurements')
  console.dir(sortly, { depth: null })
  let uniqueMeasurements = [...new Set(sortly.map(product => product.measured_quantity?.name))]
  console.log('uniqueMeasurements', uniqueMeasurements)
  let index = uniqueMeasurements.indexOf(undefined)
  if (index !== -1) {
    uniqueMeasurements.splice(index, 1)
  }
  for (let unit of uniqueMeasurements) {
    let data = setMeasurementData(unit)
    let id = crypto.randomUUID()
    let toPush = {
      type: 'MEASUREMENT_UNIT',
      id: `#${id}`,
      measurementUnitData: {
        measurementUnit: data
      }
    }
    upsertBody.batches[0].objects.push(toPush)
    console.dir(upsertBody, { depth: null })
    try {
      const response = await client.catalogApi.batchUpsertCatalogObjects(upsertBody)
      console.log('createMeasurementList', response.result)
      let data = []
      console.log(response.result.objects.length)
      if (response.result.objects.length > 1) {
        response.result.objects.forEach(measurement => {
          console.log(measurement)
          let measurementData = measurement.measurement_unit_data.measurement_unit
          console.log('hrm', measurementData)
          data.push({
            squareId: measurement.id,
            unit: setMeasurementUnit(measurementData[Object.keys(measurementData)[0]])
          })
        })
      }
      console.log('returning measurement list', data)
      return data
    } catch (error) {
      console.log(error)
    }
  }
}
//Function to create square item catalog
async function createSquareCatalog(sortly) {
  let categories = []
  let upsertBody = {
    idempotencyKey: crypto.randomUUID(),
    batches: []
  }
  let batches = {}
  let count = 0
  let error_count = 0
  sortly.forEach(product => {
    if (!categories.includes(product.parent_id)) {
      categories.push(product.parent_id)
      batches[product.parent_id] = {
        objects: []
      }
      batches[product.parent_id].objects.push({
        type: 'CATEGORY',
        id: `#${product.parent_id}`,
        categoryData: {
          name: product.category
        }
      })
    }
    if (batches[product.parent_id]) {
      let toPush = [{
        type: 'ITEM',
        id: `#${product.id}`,
        itemData: {
          name: product.name,
          categoryId: `#${product.parent_id}`,
          productType: 'REGULAR'
        }
      },
      {
        type: 'ITEM_VARIATION',
        id: `#${product.id}_variation`,
        itemVariationData: {
          itemId: `#${product.id}`,
          pricingType: 'FIXED_PRICING',
          priceMoney: {
            amount: Math.round((Math.abs(+product.price / 100) * 10000)),
            currency: 'USD'
          },
          trackInventory: true,
          sellable: true,
          sku: product.sku,
          stockable: true
        }
      }]
      if (product?.measured_quantity) {
        console.log('I should be adding measurementId to this product', product.name)
        let itemIndex = toPush.findIndex(object => object.type === 'ITEM_VARIATION')
        toPush[itemIndex].itemVariationData.measurementUnitId = measurement_list.find(measurement => measurement.unit === product.measured_quantity.name).squareId
      }
      toPush.map(item => {
        batches[product.parent_id].objects.push(item)
      })
      count++

    } else {
      error_count++
      console.log('doesnt match something', product)
      let index = categories.indexOf(product.parent_id)
    }
  })
  console.log('Number of categories: ', categories.length, 'Number of items created?', count, 'Total: ', categories.length + count)

  // Probably a better way of doing this when creating batches versus doing it afterwards
  // Going through batches and combining them where I can
  let contensedBatches = []
  let keys = Object.keys(batches)
  for (const batch in batches) {
    let batchCount = contensedBatches.length
    let batchKey = `batch${batchCount}`
    let currBatchSize = batches[batch].objects.length
    let prevBatch = keys.indexOf(batch) - 1
    if (prevBatch === -1) {
      contensedBatches.push({
        batch1: batches[batch].objects
      })
    } else {
      let prevBatchSize = contensedBatches[batchCount - 1][batchKey].length
      if (currBatchSize + prevBatchSize < 1000) {
        contensedBatches[batchCount - 1][batchKey] = [...contensedBatches[batchCount - 1][batchKey], ...batches[batch].objects]
      } else {
        batchKey = `batch${batchCount + 1}`
        contensedBatches.push({
          [batchKey]: batches[batch].objects
        })
      }
    }
  }


  for (const batch of contensedBatches) {
    let key = `batch${contensedBatches.indexOf(batch) + 1}`
    upsertBody.batches.push({ objects: batch[key] })
  }

  // Object.keys(batches).map(batch => {
  //   let length = batches[batch].objects.length
  //   upsertBody.batches.push(batches[batch])
  // })
  // console.log('upsertBody batches length', upsertBody.batches.length)
  // return

  try {
    //Running into a rate limit issue on production. I think I need to break upsertBody up into smaller chunks and do multiple calls?
    //Testing this currently with our Dev Production account
    const categoryResponse = await client.catalogApi.batchUpsertCatalogObjects(upsertBody)
    console.log(categoryResponse.result.objects.length)
    let catalog = await getCatalog('square')
    console.log('catalog created', catalog.length)
    //console.dir(catalog,{depth:null})
    return catalog
  } catch (error) {

    if (error instanceof ApiError) {
      error.result.errors.forEach(err => {
        console.log(err.code)
      })
    }
    console.log(error.statusCode)
  }

}

//Function to create new item in Square that wasnt matched
//Very similar to createSquareCatalog
//Could probably combine them
async function createNewItem(products) {
  console.log('creating new item')
  let upsertBody = {
    idempotencyKey: crypto.randomUUID(),
    batches: []
  }
  let categories = []
  let batches = {
    items: {
      objects: []
    }
  }
  console.log(products)


  let newItems = []
  for (const product of products) {
    newItems.push({
      product_name: product.name,
      square_location_id: locationId,
      sku: product.sku,
      sortly_id: product.id,
      sortly_quantity: product.quantity.toString()
    })
    //check if category exists
    console.log('On product: ', product.name);


    try {
      console.log('createNew Item: ', product)
      const categoryResponse = await client.catalogApi.searchCatalogObjects({
        objectTypes: ['CATEGORY'],
        query: {
          exactQuery: {
            attributeName: 'name',
            attributeValue: product.category
          }
        }
      })





      let toPush = []
      console.log('batches', batches)
      if (categoryResponse.result?.objects) {
        // console.log('categoryResponse', categoryResponse.result.objects)
        let squareId = categoryResponse.result.objects[0].id
        console.log('squareid', squareId)
        toPush = [{
          type: 'ITEM',
          id: `#${product.id}`,
          itemData: {
            name: product.name,
            categoryId: squareId,
            productType: 'REGULAR'
          }
        },
        {
          type: 'ITEM_VARIATION',
          id: `#${product.id}_variation`,
          itemVariationData: {
            itemId: `#${product.id}`,
            pricingType: 'FIXED_PRICING',
            priceMoney: {
              amount: Math.round((Math.abs(+product.price / 100) * 10000)),
              currency: 'USD'
            },
            trackInventory: true,
            sku: product.sku,
            sellable: true,
            stockable: true
          }
        }]
      } else {
        if (!categories.includes(product.parent_id)) {
          categories.push(product.parent_id)
          console.log('trying to push new category')
          console.log(product)
          batches[product.parent_id] = {
            objects: []
          }
          toPush = [
            {
              type: 'CATEGORY',
              id: `#${product.parent_id}`,
              categoryData: {
                name: product.category
              }
            },
            {
              type: 'ITEM',
              id: `#${product.id}`,
              itemData: {
                name: product.name,
                categoryId: `#${product.parent_id}`,
                productType: 'REGULAR'
              }
            },
            {
              type: 'ITEM_VARIATION',
              id: `#${product.id}_variation`,
              itemVariationData: {
                itemId: `#${product.id}`,
                pricingType: 'FIXED_PRICING',
                priceMoney: {
                  amount: Math.round((Math.abs(+product.price / 100) * 10000)),
                  currency: 'USD'
                },
                trackInventory: true,
                sku: product.sku,
                sellable: true,
                stockable: true
              }
            }]
        }
      }
      if (product?.measured_quantity && product.measured_quantity.name !== 'units') {
        console.log('measurement', measurement_list,product.measured_quantity)
        let measurementId = measurement_list.find(measurement => measurement.unit === product.measured_quantity.name).squareId
        let itemIndex = toPush.findIndex(object => object.type === 'ITEM_VARIATION')
        toPush[itemIndex].itemVariationData.measurementUnitId = measurementId
      }
      toPush.map(item => {
        categoryResponse.result?.objects ? batches.items.objects.push(item) :
          batches[product.parent_id].objects.push(item)
      })
    } catch (error) {
      console.log(error)
    }


  }
  console.log('upserting batch: ');
  console.dir(batches, { depth: null })
  Object.keys(batches).map(batch => {
    console.log('making upsertBody:')
    console.log(batches[batch])
    upsertBody.batches.push({ objects: batches[batch].objects })
  })
  console.dir(upsertBody, { depth: null })
  try {
    const response = await client.catalogApi.batchUpsertCatalogObjects(
      upsertBody
    );
    console.log("product(s) inserted", newItems);
    let newProducts = await response.result.objects;
    for (const product of newProducts) {
      console.log("adding more stuff to newItem product");
      let itemIndex = newItems.findIndex(
        (item) =>
          item.sku === product.itemData.variations[0].itemVariationData.sku
      );
      Object.assign(newItems[itemIndex], {
        square_id: product.itemData.variations[0].id,
        square_data: product.itemData,
        square_type: product.type,
      });
    }
    return newItems;
  } catch (error) {
    console.log(error);
  }
}
//Function to set measurement data
function setMeasurementData(unit) {
  console.log('setting data', unit)
  let data = {}
  switch (unit) {
    case 'in':
      data.lengthUnit = 'IMPERIAL_INCH'
      data.type = 'TYPE_LENGTH'
      break;
    case 'ft':
      console.log('case is ft')
      data.lengthUnit = 'IMPERIAL_FOOT'
      data.type = 'TYPE_LENGTH'
      break;
    case 'yd':
      data.lengthUnit = 'IMPERIAL_YARD'
      data.type = 'TYPE_LENGTH'
      break;
    case 'cm':
      data.lengthUnit = 'METRIC_CENTIMETER'
      data.type = 'TYPE_LENGTH'
      break;
    case 'm':
      data.lengthUnit = 'METRIC_METER'
      data.type = 'TYPE_LENGTH'
      break;
    case 'oz':
      data.weightUnit = 'IMPERIAL_WEIGHT_OUNCE'
      data.type = 'TYPE_WEIGHT'
      break;
    case 'lbs':
      data.weightUnit = 'IMPERIAL_POUNDS'
      data.type = 'TYPE_WEIGHT'
      break;
    case 'g':
      data.weightUnit = 'METRIC_GRAM'
      data.type = 'TYPE_WEIGHT'
      break;
    case 'kg':
      data.weightUnit = 'METRIC_KILOGRAM'
      data.type = 'TYPE_WEIGHT'
      break;
    case 'gal':
      data.volumeUnit = 'GENERIC_GALLON'
      data.type = 'TYPE_VOLUME'
      break;
    case 'ml':
      data.volumeUnit = 'METRIC_MILLILITER'
      data.type = 'TYPE_VOLUME'
      break;
    case 'l':
      data.volumeUnit = 'METRIC_LITER'
      data.type = 'TYPE_VOLUME'
      break;
    case 'ft3':
      data.volumeUnit = 'IMPERIAL_CUBIC_FOOT'
      data.type = 'TYPE_VOLUME'
      break;
    case 'in3':
      data.volumeUnit = 'IMPERIAL_CUBIC_INCH'
      data.type = 'TYPE_VOLUME'
      break;
    case 'ft2':
      data.type = 'TYPE_AREA'
      data.areaUnit = 'IMPERIAL_SQUARE_FOOT'
      break;
    case 'in2':
      data.type = 'TYPE_AREA'
      data.areaUnit = 'IMPERIAL_SQUARE_INCH'
      break;
    default:
      break;
  }
  console.log(data)
  return data
}
function setMeasurementUnit(unit) {
  switch (unit) {
    case 'IMPERIAL_INCH':
      return 'in'
      break;
    case 'IMPERIAL_FOOT':
      return 'ft'
      break;
    case 'IMPERIAL_YARD':
      return 'yd'
      break;
    case 'METRIC_CENTIMETER':
      return 'cm'
      break;
    case 'METRIC_METER':
      return 'm'
      break;
    case 'IMPERIAL_WEIGHT_OUNCE':
      return 'oz'
      break;
    case 'IMPERIAL_POUNDS':
      return 'lbs'
      break;
    case 'METRIC_GRAM':
      return 'g'
      break;
    case 'METRIC_KILOGRAM':
      return 'kg'
      break;
    case 'IMPERIAL_CUBIC_FEET':
      return 'ft3'
      break;
    case 'IMPERIAL_CUBIC_INCH':
      return 'in3'
      break;
    case 'IMPERIAL_SQUARE_FEET':
      return 'ft2'
      break;
    case 'IMPERIAL_SQUARE_INCH':
      return 'in2'
      break;
    case 'GENERIC_GALLON':
      return 'gal'
      break;
    case 'METRIC_LITER':
      return 'l'
      break;
    case 'METRIC_MILILITER':
      return 'ml'
      break;
    default:
      break;
  }
}
async function getCategory(categoryId) {
  try {
    const response = await client.catalogApi.retrieveCatalogObject(categoryId)
    let category = response.result.object.categoryData.name
    return category
  } catch (err) {
    console.log(err)
  }
}
async function getMoreItems(cursor, currentList) {
  console.log('filling out catalog')
  let catalog = currentList ? [...currentList] : []
  try {
    const categoryResponse = await client.catalogApi.listCatalog(cursor, 'ITEM')
    catalog = [...catalog, ...categoryResponse.result.objects]
    if (categoryResponse.result?.cursor) {
      return getMoreItems(categoryResponse.result.cursor, catalog)
    } else {
      return catalog
    }
  } catch (error) {
    console.log(error)
  }
}
//Update Sortly Inventory from Square
async function updateSortlyInventory(incomingEvent, combinedData) {
  console.log('updating sortly time')
  let toUpdate = {}
  // console.dir(toUpdate,{depth:null})
  incomingEvent.object.inventory_counts.forEach(event => {
    if (event.state === 'IN_STOCK') {
      combinedData.forEach(item => {
        if (item.square_id === event.catalog_object_id) {
          if (item.sortly_quantity === event.quantity) {
            console.log('quanties match no need to update')
            return
          }
          toUpdate.method = 'put'
          toUpdate.url = `${sortlyURL}/items/${item.sortly_id}`,
            toUpdate.headers = {
              'Authorization': 'Bearer sk_sortly_8JuAorUPwXxXFaP6XhrB',
              'Content-Type': 'application/json'
            }
          let newQuantity = +event.quantity < 0 ? 0 : +event.quantity
          toUpdate.data = {
            name: item.product_name,
            type: 'item',
            quantity: newQuantity
          }
          console.log('update sortly stock')
          console.log(`Updating ${item.product_name} from ${item.sortly_quantity} to ${event.quantity}`)
          axios(toUpdate)
            .then((categoryResponse) => {
              if (categoryResponse.status === 204) {
                console.log('Sortly is updated')
              }
            }, (error) => {
              console.dir(error);
            });

        }
      })
    }

  })

  //console.log(data)
}
//Helper Functions
function sleep(ms) {
  return new Promise((resolve) => {
    setTimeout(resolve, ms)
  })
}

function getSmallest(arr) {
  return arr.reduce((a, b) => Math.min(a, b))
}
function chunk(arr, size) {
  let returnArray = []
  for (let i = 0; i < arr.length; i += size) {
    returnArray.push(arr.slice(i, i + size));
  }
  return returnArray
}

